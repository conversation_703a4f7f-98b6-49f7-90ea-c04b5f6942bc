Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FE8E
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210286019, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E80  000210068E24 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9160  00021006A225 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD35D40000 ntdll.dll
7FFD35060000 KERNEL32.DLL
7FFD332A0000 KERNELBASE.dll
7FFD2F680000 apphelp.dll
7FFD35A60000 USER32.dll
7FFD33810000 win32u.dll
000210040000 msys-2.0.dll
7FFD33DD0000 GDI32.dll
7FFD32E90000 gdi32full.dll
7FFD33840000 msvcp_win.dll
7FFD33990000 ucrtbase.dll
7FFD35C40000 advapi32.dll
7FFD33AE0000 msvcrt.dll
7FFD349A0000 sechost.dll
7FFD35840000 RPCRT4.dll
7FFD32590000 CRYPTBASE.DLL
7FFD338F0000 bcryptPrimitives.dll
7FFD33E00000 IMM32.DLL
